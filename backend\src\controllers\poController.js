const { PO, Program, PLO } = require('../models');

exports.getAllPOs = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const pos = await PO.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: Program, attributes: ['program_id', 'name'] },
                { model: PLO, through: { attributes: [] }, attributes: ['plo_id', 'description'] },
            ],
        });

        res.status(200).json({
            totalItems: pos.count,
            totalPages: Math.ceil(pos.count / limit),
            currentPage: parseInt(page),
            pos: pos.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách PO', error: error.message });
    }
};

exports.getPOById = async (req, res) => {
    try {
        const po = await PO.findByPk(req.params.id, {
            include: [
                { model: Program, attributes: ['program_id', 'name'] },
                { model: PLO, through: { attributes: [] }, attributes: ['plo_id', 'description'] },
            ],
        });

        if (!po) return res.status(404).json({ message: 'PO không tồn tại' });
        res.status(200).json(po);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin PO', error: error.message });
    }
};

exports.createPO = async (req, res) => {
    try {
        const { name, description, program_id } = req.body;

        if (!name || !program_id) {
            return res.status(400).json({ message: 'Thiếu các trường bắt buộc' });
        }

        const program = await Program.findByPk(program_id);
        if (!program) {
            return res.status(400).json({ message: 'Chương trình không tồn tại' });
        }

        const newPO = await PO.create({ name, description, program_id });
        res.status(201).json(newPO);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo PO', error: error.message });
    }
};

exports.updatePO = async (req, res) => {
    try {
        const { name, description, program_id } = req.body;

        const po = await PO.findByPk(req.params.id);
        if (!po) return res.status(404).json({ message: 'PO không tồn tại' });

        if (program_id) {
            const program = await Program.findByPk(program_id);
            if (!program) return res.status(400).json({ message: 'Chương trình không tồn tại' });
        }

        await po.update({
            name: name || po.name,
            description: description || po.description,
            program_id: program_id || po.program_id,
        });

        res.status(200).json(po);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật PO', error: error.message });
    }
};

exports.deletePO = async (req, res) => {
    try {
        const po = await PO.findByPk(req.params.id);
        if (!po) return res.status(404).json({ message: 'PO không tồn tại' });

        await po.destroy();
        res.status(200).json({ message: 'Xóa PO thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa PO', error: error.message });
    }
};