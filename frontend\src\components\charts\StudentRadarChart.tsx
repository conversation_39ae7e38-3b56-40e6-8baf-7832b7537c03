"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Loader2,
  Bar<PERSON>hart3,
  Clock,
  Target,
  TrendingUp,
  Award,
} from "lucide-react";
import { quizService } from "@/services/api";
import { UserRadarData, RadarChartConfig, AllRadarData } from "@/types/radar";
import { showErrorToast } from "@/lib/toast-utils";
import Radar<PERSON><PERSON>, { transformRadarData, colorSchemes } from "./RadarChart";
import ChapterRecommendations from "@/components/learning/ChapterRecommendations";

interface StudentRadarChartProps {
  quizId: number;
  quizName?: string;
  className?: string;
}

export default function StudentRadarChart({
  quizId,
  quizName,
  className = "",
}: StudentRadarChartProps) {
  const [radarData, setRadarData] = useState<UserRadarData | null>(null);
  const [combinedRadarData, setCombinedRadarData] =
    useState<AllRadarData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRadarData = async () => {
      try {
        setIsLoading(true);

        // Gọi 3 API song song
        const [currentUserData, averageData, topPerformerData] =
          await Promise.allSettled([
            quizService.getCurrentUserRadarData(quizId),
            quizService.getAverageRadarData(quizId),
            quizService.getTopPerformerRadarData(quizId),
          ]);

        // Lưu dữ liệu người dùng hiện tại (để hiển thị thông tin chi tiết)
        if (currentUserData.status === "fulfilled") {
          setRadarData(currentUserData.value);
        }

        // Tạo dữ liệu kết hợp cho radar chart chồng lại
        const processedData: AllRadarData = {
          quiz_id: quizId,
          quiz_name: quizName || "Bài kiểm tra",
          total_questions: 0,
          radar_data: {},
          summary: {
            total_participants: 0,
            total_answers: 0,
            average_score: 0,
            difficulty_levels: [],
            learning_outcomes: [],
          },
        };

        // Xử lý dữ liệu người dùng hiện tại
        if (currentUserData.status === "fulfilled") {
          processedData.radar_data.current_user = {
            user_id: currentUserData.value.user_id,
            data: currentUserData.value.radar_data,
          };
        }

        // Xử lý dữ liệu trung bình
        if (averageData.status === "fulfilled") {
          processedData.radar_data.average = averageData.value.radar_data;
        }

        // Xử lý dữ liệu top performer
        if (topPerformerData.status === "fulfilled") {
          processedData.radar_data.top_performer = {
            user_info: topPerformerData.value.top_performer,
            data: topPerformerData.value.radar_data,
          };
        }

        setCombinedRadarData(processedData);
        setError(null);
      } catch (err) {
        console.error("Lỗi khi lấy dữ liệu radar chart:", err);
        setError("Không thể tải dữ liệu phân tích. Vui lòng thử lại sau.");
        showErrorToast("Không thể tải dữ liệu phân tích");
      } finally {
        setIsLoading(false);
      }
    };

    if (quizId) {
      fetchRadarData();
    }
  }, [quizId]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex justify-center items-center py-20">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
            <span className="text-lg font-medium text-muted-foreground">
              Đang tải dữ liệu phân tích...
            </span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !radarData) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center py-20">
          <BarChart3 className="h-16 w-16 text-muted-foreground mb-4" />
          <p className="text-lg font-medium text-muted-foreground mb-2">
            {error || "Không có dữ liệu phân tích"}
          </p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Thử lại
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Tạo dữ liệu cho chart chồng lại (tương tự TeacherRadarChart)
  const getComparisonChartData = (): RadarChartConfig => {
    if (!combinedRadarData) {
      // Fallback về chart đơn nếu không có dữ liệu kết hợp
      return transformRadarData(
        radarData.radar_data,
        "Kết quả của bạn",
        colorSchemes.primary
      );
    }

    const datasets = [];

    // 1. Thêm dữ liệu trung bình (vòng ngoài cùng - màu xanh dương)
    if (combinedRadarData.radar_data.average) {
      const avgData = transformRadarData(
        combinedRadarData.radar_data.average,
        "Trung bình lớp",
        colorSchemes.primary
      );
      const customizedDataset = {
        ...avgData.datasets[0],
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        borderColor: "rgb(59, 130, 246)",
        pointBackgroundColor: "rgb(59, 130, 246)",
        pointBorderColor: "#fff",
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderColor: "rgb(59, 130, 246)",
      };
      datasets.push(customizedDataset);
    }

    // 2. Thêm dữ liệu top performer (vòng giữa - màu xanh lá)
    if (combinedRadarData.radar_data.top_performer) {
      const topData = transformRadarData(
        combinedRadarData.radar_data.top_performer.data,
        `Học viên xuất sắc: ${combinedRadarData.radar_data.top_performer.user_info.name}`,
        colorSchemes.success
      );
      const customizedDataset = {
        ...topData.datasets[0],
        backgroundColor: "rgba(34, 197, 94, 0.15)",
        borderColor: "rgb(34, 197, 94)",
        pointBackgroundColor: "rgb(34, 197, 94)",
        pointBorderColor: "#fff",
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderColor: "rgb(34, 197, 94)",
      };
      datasets.push(customizedDataset);
    }

    // 3. Thêm dữ liệu user hiện tại (vòng trong cùng - màu cam)
    if (combinedRadarData.radar_data.current_user) {
      const currentData = transformRadarData(
        combinedRadarData.radar_data.current_user.data,
        "Kết quả của bạn",
        colorSchemes.warning
      );
      const customizedDataset = {
        ...currentData.datasets[0],
        backgroundColor: "rgba(249, 115, 22, 0.2)",
        borderColor: "rgb(249, 115, 22)",
        pointBackgroundColor: "rgb(249, 115, 22)",
        pointBorderColor: "#fff",
        pointHoverBackgroundColor: "#fff",
        pointHoverBorderColor: "rgb(249, 115, 22)",
      };
      datasets.push(customizedDataset);
    }

    // Lấy labels từ dataset đầu tiên có sẵn
    const labels = combinedRadarData.radar_data.average
      ? transformRadarData(
          combinedRadarData.radar_data.average,
          "",
          colorSchemes.primary
        ).labels
      : combinedRadarData.radar_data.current_user
      ? transformRadarData(
          combinedRadarData.radar_data.current_user.data,
          "",
          colorSchemes.warning
        ).labels
      : [];

    return { labels, datasets };
  };

  const chartData: RadarChartConfig = getComparisonChartData();

  // Tính toán các metrics để hiển thị
  const metrics = radarData.radar_data.performance_metrics;
  const difficultyLevels = radarData.radar_data.difficulty_levels;
  const learningOutcomes = radarData.radar_data.learning_outcomes;

  // Tìm điểm mạnh và điểm yếu
  const getStrengthsAndWeaknesses = () => {
    const allData = [
      ...Object.entries(difficultyLevels).map(([key, value]) => ({
        name: `Mức độ ${key}`,
        accuracy: value.accuracy,
        type: "difficulty",
      })),
      ...Object.entries(learningOutcomes).map(([key, value]) => ({
        name: key,
        accuracy: value.accuracy,
        type: "learning_outcome",
      })),
    ];

    const sorted = allData.sort((a, b) => b.accuracy - a.accuracy);
    const strengths = sorted.slice(0, 2);
    const weaknesses = sorted.slice(-2).reverse();

    return { strengths, weaknesses };
  };

  const { strengths, weaknesses } = getStrengthsAndWeaknesses();

  // Format thời gian
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <CardTitle className="text-xl sm:text-2xl flex items-center gap-2">
              <BarChart3 className="h-6 w-6" />
              Phân tích kết quả của bạn
            </CardTitle>
            {quizName && <p className="text-muted-foreground">{quizName}</p>}
            {radarData?.message && (
              <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded mt-2">
                {radarData.message}
              </p>
            )}
          </div>

          {/* Legend cho 3 vòng chồng lên nhau (chỉ hiển thị khi có dữ liệu kết hợp) */}
          {combinedRadarData && (
            <div className="text-sm">
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="font-medium text-gray-700 mb-2">So sánh:</div>
                <div className="flex flex-wrap gap-4">
                  {combinedRadarData.radar_data.average && (
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                      <span className="text-gray-600">Trung bình lớp</span>
                    </div>
                  )}
                  {combinedRadarData.radar_data.top_performer && (
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span className="text-gray-600">Học viên xuất sắc</span>
                    </div>
                  )}
                  {combinedRadarData.radar_data.current_user && (
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                      <span className="text-gray-600">Kết quả của bạn</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {/* Radar Chart */}
        <div className="mb-8">
          <RadarChart
            data={chartData}
            title={
              combinedRadarData
                ? "So sánh kết quả"
                : "Biểu đồ phân tích năng lực"
            }
            height={400}
          />
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium text-blue-600">
                Độ chính xác tổng thể
              </span>
            </div>
            <div className="text-2xl font-bold text-blue-700">
              {metrics.overall_accuracy}%
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Award className="h-5 w-5 text-green-600" />
              <span className="text-sm font-medium text-green-600">
                Lần đầu đúng
              </span>
            </div>
            <div className="text-2xl font-bold text-green-700">
              {metrics.first_attempt_accuracy}%
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <span className="text-sm font-medium text-purple-600">
                Tỷ lệ hoàn thành
              </span>
            </div>
            <div className="text-2xl font-bold text-purple-700">
              {metrics.completion_rate}%
            </div>
          </div>

          <div className="bg-orange-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-5 w-5 text-orange-600" />
              <span className="text-sm font-medium text-orange-600">
                Thời gian TB
              </span>
            </div>
            <div className="text-2xl font-bold text-orange-700">
              {formatTime(metrics.average_response_time)}
            </div>
          </div>
        </div>

        {/* Strengths and Weaknesses */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Điểm mạnh */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-3 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Điểm mạnh
            </h4>
            <div className="space-y-2">
              {strengths.map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm text-green-700">{item.name}</span>
                  <span className="text-sm font-medium text-green-800">
                    {item.accuracy}%
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Điểm cần cải thiện */}
          <div className="bg-red-50 p-4 rounded-lg">
            <h4 className="font-semibold text-red-800 mb-3 flex items-center gap-2">
              <Target className="h-4 w-4" />
              Cần cải thiện
            </h4>
            <div className="space-y-2">
              {weaknesses.map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm text-red-700">{item.name}</span>
                  <span className="text-sm font-medium text-red-800">
                    {item.accuracy}%
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Weakest Areas and Recommendations */}
        {(radarData.weakest_lo || radarData.weakest_difficulty) && (
          <div className="mt-8 bg-amber-50 border border-amber-200 rounded-lg p-6">
            <h4 className="font-semibold text-amber-800 mb-4 flex items-center gap-2">
              <Target className="h-5 w-5" />
              Đề xuất cải thiện
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {radarData.weakest_lo && (
                <div className="bg-white p-4 rounded-lg border border-amber-200">
                  <h5 className="font-medium text-amber-800 mb-2">
                    Chuẩn đầu ra cần cải thiện nhất
                  </h5>
                  <div className="text-sm text-amber-700 mb-2">
                    <strong>{radarData.weakest_lo.lo_name}</strong>
                  </div>
                  <div className="text-xs text-amber-600 mb-3">
                    Độ chính xác: {radarData.weakest_lo.accuracy.toFixed(1)}%
                  </div>

                  {radarData.weakest_lo.chapters &&
                    radarData.weakest_lo.chapters.length > 0 && (
                      <div>
                        <p className="text-xs font-medium text-amber-700 mb-2">
                          Chương học liên quan:
                        </p>
                        <div className="space-y-2">
                          {radarData.weakest_lo.chapters
                            .slice(0, 2)
                            .map((chapter) => (
                              <div
                                key={chapter.chapter_id}
                                className="text-xs text-amber-600 bg-amber-100 p-2 rounded"
                              >
                                <div className="font-medium">
                                  {chapter.chapter_name}
                                </div>
                                {chapter.description && (
                                  <div className="text-amber-500 mt-1">
                                    {chapter.description}
                                  </div>
                                )}
                              </div>
                            ))}
                          {radarData.weakest_lo.chapters.length > 2 && (
                            <div className="text-xs text-amber-500">
                              +{radarData.weakest_lo.chapters.length - 2} chương
                              khác
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                </div>
              )}

              {radarData.weakest_difficulty && (
                <div className="bg-white p-4 rounded-lg border border-amber-200">
                  <h5 className="font-medium text-amber-800 mb-2">
                    Mức độ khó cần cải thiện nhất
                  </h5>
                  <div className="text-sm text-amber-700 mb-2">
                    <strong>Mức độ {radarData.weakest_difficulty.level}</strong>
                  </div>
                  <div className="text-xs text-amber-600">
                    Độ chính xác:{" "}
                    {radarData.weakest_difficulty.accuracy.toFixed(1)}%
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Detailed breakdown */}
        <div className="mt-8">
          <h4 className="font-semibold text-gray-800 mb-4">
            Chi tiết theo từng mảng
          </h4>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Difficulty Levels */}
            <div>
              <h5 className="font-medium text-gray-700 mb-3">
                Theo mức độ khó
              </h5>
              <div className="space-y-3">
                {Object.entries(difficultyLevels).map(([level, data]) => (
                  <div key={level} className="bg-gray-50 p-3 rounded">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium capitalize">
                        {level}
                      </span>
                      <span className="text-sm text-gray-600">
                        {data.accuracy}%
                      </span>
                    </div>
                    <div className="text-xs text-gray-500">
                      {data.questions_count} câu • TB:{" "}
                      {formatTime(data.average_response_time)}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Learning Outcomes */}
            <div>
              <h5 className="font-medium text-gray-700 mb-3">
                Theo chuẩn đầu ra
              </h5>
              <div className="space-y-3">
                {Object.entries(learningOutcomes).map(([lo, data]) => (
                  <div key={lo} className="bg-gray-50 p-3 rounded">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium">{lo}</span>
                      <span className="text-sm text-gray-600">
                        {data.accuracy}%
                      </span>
                    </div>
                    {data.description && (
                      <div className="text-xs text-gray-600 mb-1 italic">
                        {data.description}
                      </div>
                    )}
                    <div className="text-xs text-gray-500">
                      {data.questions_count} câu • TB:{" "}
                      {formatTime(data.average_response_time)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Component wrapper để hiển thị cả radar chart và chapter recommendations
interface StudentRadarWithRecommendationsProps {
  quizId: number;
  quizName?: string;
  className?: string;
}

export function StudentRadarWithRecommendations({
  quizId,
  quizName,
  className = "",
}: StudentRadarWithRecommendationsProps) {
  const [radarData, setRadarData] = useState<UserRadarData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRadarData = async () => {
      try {
        setIsLoading(true);
        // Chỉ cần lấy dữ liệu current user để hiển thị recommendations
        const data = await quizService.getCurrentUserRadarData(quizId);
        setRadarData(data);
      } catch (err) {
        console.error("Lỗi khi lấy dữ liệu radar chart:", err);
      } finally {
        setIsLoading(false);
      }
    };

    if (quizId) {
      fetchRadarData();
    }
  }, [quizId]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Radar Chart */}
      <StudentRadarChart
        quizId={quizId}
        quizName={quizName}
        className="w-full"
      />

      {/* Chapter Recommendations */}
      {!isLoading && radarData?.weakest_lo && (
        <ChapterRecommendations
          weakestLO={radarData.weakest_lo}
          className="w-full"
        />
      )}
    </div>
  );
}
