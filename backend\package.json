{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon src/server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dotenv": "^16.4.7", "express": "^5.1.0", "fast-csv": "^5.0.2", "firebase-admin": "^13.2.0", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "multer": "^1.4.5-lts.2", "pg": "^8.14.1", "pg-hstore": "^2.3.4", "redis": "^4.7.0", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.2", "socket.io": "^4.8.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.9"}}