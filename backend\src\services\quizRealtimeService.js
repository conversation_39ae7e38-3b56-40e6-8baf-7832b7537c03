const { db } = require('../config/firebase');
const { setCache, getCache } = require('../redis/utils');
const { Quiz, Question, Answer, LO, UserLOTracking, UserQuizTracking, UserQuestionHistory, ChapterLO, Chapter, QuizResult, User } = require('../models');

class QuizRealtimeService {
    constructor(io) {
        this.io = io;
    }

    async saveRealtimeAnswer(quizId, userId, questionId, answerId, isCorrect, responseTime) {
        try {
            // Log khi có request với user_id = 10
            if (userId === '10') {
                console.log('saveRealtimeAnswer called with user_id = 10:');
                console.log('Quiz ID:', quizId);
                console.log('Question ID:', questionId);
                console.log('Answer ID:', answerId);
                console.log('Is Correct:', isCorrect);
                console.log('Response Time:', responseTime);
                console.log('Stack trace:', new Error().stack);
            }

            // Validate input parameters
            if (!quizId || !userId || !questionId || !answerId) {
                console.error('Missing required parameters:', { quizId, userId, questionId, answerId });
                return;
            }

            // Validate response time
            if (responseTime < 0 || responseTime > 30000) {
                console.error('Invalid response time:', responseTime);
                return;
            }

            const quizRef = db.ref(`quiz_sessions/${quizId}`);
            const participantRef = quizRef.child('participants').child(userId);
            const answerRef = participantRef.child('answers').child(questionId);

            // Kiểm tra user tồn tại trước khi tiếp tục
            const user = await User.findByPk(userId, {
                attributes: ['user_id', 'name', 'email']
            });
            if (!user) {
                console.error(`User ${userId} not found`);
                return;
            }

            // Lấy thông tin quiz để biết tổng số câu hỏi
            const quiz = await Quiz.findByPk(quizId, {
                include: [{
                    model: Question,
                    as: 'Questions',
                    through: { attributes: [] },
                    attributes: ['question_id']
                }]
            });

            if (!quiz) {
                console.error(`Quiz ${quizId} not found`);
                return;
            }

            // Kiểm tra trạng thái quiz
            if (quiz.status !== 'active') {
                console.error(`Quiz ${quizId} is not active`);
                return;
            }

            const totalQuestions = quiz.Questions.length;

            // Lấy dữ liệu hiện tại của người dùng
            const participantSnapshot = await participantRef.once('value');
            const currentData = participantSnapshot.val() || {
                total_answers: 0,
                correct_answers: 0,
                current_score: 0,
                answers: {}
            };

            // Kiểm tra nếu người dùng đã hoàn thành quiz
            if (currentData.status === 'completed') {
                console.error(`User ${userId} has already completed quiz ${quizId}`);
                return;
            }

            let existingAnswer = currentData.answers && currentData.answers[questionId];
            let attempts = existingAnswer ? (existingAnswer.attempts || 0) : 0;

            // Nếu đã đúng hoặc đã trả lời 2 lần (sau khi xử lý lần trả lời trước đó) thì không cho trả lời nữa
            if (existingAnswer && (existingAnswer.is_correct || attempts >= 2)) {
                console.log(`Question ${questionId} already answered correctly or max attempts reached. User: ${userId}`);
                return;
            }

            attempts += 1; // Tăng số lần thử cho lần trả lời hiện tại

            // Tính điểm cho câu hỏi này
            let questionScore = 0;
            if (isCorrect) {
                questionScore = (attempts === 1) ? 10 : 5; // 10 điểm cho lần đầu đúng, 5 điểm cho lần 2 đúng
            }

            // Lưu câu trả lời
            const answerData = {
                answer_id: answerId,
                is_correct: isCorrect,
                response_time: responseTime,
                timestamp: Date.now(),
                attempts: attempts,
                score: questionScore
            };

            // Cập nhật answers
            const newAnswers = {
                ...currentData.answers,
                [questionId]: answerData
            };

            // Tính lại tổng điểm
            let totalScore = 0;
            let totalCorrect = 0;
            let totalAnswers = 0;
            Object.values(newAnswers).forEach(ans => {
                totalScore += ans.score || 0;
                if (ans.is_correct) totalCorrect += 1;
                totalAnswers += ans.attempts || 1;
            });

            // Cập nhật dữ liệu người dùng
            const newData = {
                ...currentData,
                answers: newAnswers,
                current_score: totalScore,
                correct_answers: totalCorrect,
                total_answers: totalAnswers,
                current_question_id: questionId,
                last_answer_time: Date.now(),
                status: 'in_progress',
            };

            // Đảm bảo không có giá trị undefined
            Object.keys(newData).forEach(key => {
                if (newData[key] === undefined) {
                    newData[key] = null;
                }
            });

            // Cập nhật dữ liệu người dùng
            try {
                await participantRef.set(newData);
            } catch (error) {
                console.error('Error updating participant data:', error);
                return;
            }

            // Lưu lịch sử câu hỏi vào database
            try {
                await UserQuestionHistory.create({
                    user_id: userId,
                    question_id: questionId,
                    quiz_id: quizId,
                    selected_answer: answerId,
                    is_correct: isCorrect,
                    time_spent: responseTime,
                    attempt_date: new Date(),
                    difficulty_level: null
                });
            } catch (error) {
                console.error('Error saving question history:', error);
                // Không throw error để không ảnh hưởng đến trải nghiệm người dùng
            }

            // Lấy danh sách câu hỏi từ cache để xác định thứ tự đúng
            let questions = await getCache(`quiz:${quizId}:questions`);
            let questionIds;
            if (questions && Array.isArray(questions)) {
                questionIds = questions.map(q => q.question_id);
            } else {
                // Fallback lấy từ DB nếu cache không có
                questions = quiz.Questions;
                questionIds = questions.map(q => q.question_id);
            }
            const currentQuestionIndex = questionIds.indexOf(questionId);

            // Nếu đây là câu hỏi cuối cùng, kết thúc quiz cho người chơi này
            if (currentQuestionIndex === questionIds.length - 1) {
                try {
                    await participantRef.update({
                        status: 'completed',
                        completed_at: Date.now()
                    });
                } catch (error) {
                    console.error('Error updating quiz completion status:', error);
                }
            }

            // Cập nhật bảng xếp hạng
            try {
                await this.updateRealtimeLeaderboard(quizId);
            } catch (error) {
                console.error('Error updating leaderboard:', error);
            }

            // Gửi kết quả trả lời
            if (this.io) {
                this.io.to(`quiz:${quizId}:${userId}`).emit('showAnswerResult', {
                    quiz_id: quizId,
                    question_id: questionId,
                    is_correct: isCorrect,
                    attempts: attempts,
                    score: questionScore,
                    total_score: totalScore
                });
            }
        } catch (error) {
            console.error('Error in saveRealtimeAnswer:', error);
            // Không throw error để không ảnh hưởng đến trải nghiệm người dùng
        }
    }

    async updateRealtimeLeaderboard(quizId) {
        const quizRef = db.ref(`quiz_sessions/${quizId}`);
        const participantsRef = quizRef.child('participants');

        // Lấy tất cả người tham gia
        const snapshot = await participantsRef.once('value');
        const participants = snapshot.val();

        if (!participants) return;

        // Lấy thông tin về câu hỏi của quiz
        const quiz = await Quiz.findByPk(quizId, {
            include: [{
                model: Question,
                as: 'Questions',
                through: { attributes: [] },
                attributes: ['question_id']
            }]
        });

        if (!quiz) return;

        const questions = quiz.Questions.map(q => q.question_id);
        const totalQuestions = questions.length;

        // Chuyển đổi dữ liệu thành mảng và sắp xếp
        const leaderboard = Object.entries(participants)
            .map(([userId, data]) => {
                const currentQuestionIndex = questions.indexOf(data.current_question_id);
                const progress = currentQuestionIndex >= 0 ? (currentQuestionIndex + 1) / totalQuestions : 0;
                const score = data.current_score || 0;
                const correctAnswers = data.correct_answers || 0;
                const totalAnswers = data.total_answers || 0;
                const accuracy = totalAnswers > 0 ? (correctAnswers / totalAnswers) * 100 : 0;
                const averageResponseTime = totalAnswers > 0 ?
                    (data.total_response_time || 0) / totalAnswers : 0;

                return {
                    user_id: userId,
                    score,
                    correct_answers: correctAnswers,
                    total_answers: totalAnswers,
                    accuracy,
                    progress,
                    current_question_id: data.current_question_id,
                    question_index: currentQuestionIndex,
                    average_response_time: averageResponseTime,
                    last_answer_time: data.last_answer_time || 0,
                    status: data.status || 'in_progress'
                };
            })
            .sort((a, b) => {
                // 1. Ưu tiên người đã hoàn thành quiz
                if (a.status === 'completed' && b.status !== 'completed') return -1;
                if (a.status !== 'completed' && b.status === 'completed') return 1;

                // 2. Nếu cả hai đều chưa hoàn thành hoặc đã hoàn thành
                // Sắp xếp theo tiến độ (progress)
                if (a.progress !== b.progress) {
                    return b.progress - a.progress;
                }

                // 3. Nếu tiến độ bằng nhau, sắp xếp theo điểm số
                if (b.score !== a.score) {
                    return b.score - a.score;
                }

                // 4. Nếu điểm số bằng nhau, sắp xếp theo độ chính xác
                if (b.accuracy !== a.accuracy) {
                    return b.accuracy - a.accuracy;
                }

                // 5. Nếu độ chính xác bằng nhau, sắp xếp theo thời gian trả lời trung bình
                if (a.average_response_time !== b.average_response_time) {
                    return a.average_response_time - b.average_response_time;
                }

                // 6. Cuối cùng, sắp xếp theo thời gian trả lời cuối cùng
                return a.last_answer_time - b.last_answer_time;
            });

        // Lưu bảng xếp hạng vào Firebase
        const leaderboardData = {};
        for (let i = 0; i < leaderboard.length; i++) {
            const item = leaderboard[i];
            const previousPosition = await this.getPreviousPosition(quizId, item.user_id);
            leaderboardData[item.user_id] = {
                ...item,
                position: i + 1,
                previous_position: previousPosition || i + 1
            };
        }
        await quizRef.child('leaderboard').set(leaderboardData);

        // Gửi cập nhật qua Socket.IO
        this.io.to(`quiz:${quizId}`).emit('leaderboardUpdate', {
            leaderboard: leaderboard.map((item, index) => ({
                ...item,
                position: index + 1,
                previous_position: leaderboardData[item.user_id].previous_position
            })),
            timestamp: Date.now()
        });
    }

    async getPreviousPosition(quizId, userId) {
        try {
            const leaderboard = await this.getRealtimeLeaderboard(quizId);
            const userPosition = leaderboard.findIndex(item => item.user_id === userId);

            if (userPosition === -1) {
                return {
                    position: 0,
                    score: 0,
                    totalParticipants: leaderboard.length
                };
            }

            return {
                position: userPosition + 1,
                score: leaderboard[userPosition].score,
                totalParticipants: leaderboard.length
            };
        } catch (error) {
            console.error('Error getting user position:', error);
            return {
                position: 0,
                score: 0,
                totalParticipants: 0
            };
        }
    }

    // Lấy bảng xếp hạng realtime từ Firebase
    async getRealtimeLeaderboard(quizId) {
        try {
            // Lấy dữ liệu từ Firebase
            const participantsRef = db.ref(`quiz_sessions/${quizId}/participants`);
            const snapshot = await participantsRef.once('value');
            const participants = snapshot.val();

            if (!participants) return [];

            // Lấy thông tin quiz để biết tổng số câu hỏi
            const quiz = await Quiz.findByPk(quizId, {
                include: [{
                    model: Question,
                    as: 'Questions',
                    through: { attributes: [] },
                    attributes: ['question_id']
                }]
            });

            if (!quiz) return [];

            const totalQuestions = quiz.Questions.length;
            const pointsPerQuestion = 10 / totalQuestions;

            // Chuyển đổi thành mảng và tính toán điểm số
            const leaderboardData = await Promise.all(
                Object.entries(participants).map(async ([userId, data]) => {
                    // Lấy thông tin người dùng từ database thay vì Firebase
                    let userName = 'Unknown';
                    try {
                        const user = await User.findByPk(userId, {
                            attributes: ['user_id', 'name', 'email']
                        });
                        if (user) {
                            userName = user.name;
                        }
                    } catch (error) {
                        console.error(`Error fetching user ${userId}:`, error);
                    }

                    // Tính toán điểm số dựa trên tỷ lệ câu đúng
                    const correctAnswers = data.correct_answers || 0;
                    const totalAnswers = data.total_answers || 0;
                    const accuracy = totalAnswers > 0 ? (correctAnswers / totalAnswers) * 100 : 0;

                    // Sử dụng điểm số đã lưu trong Firebase thay vì tính lại
                    const score = data.current_score || 0;

                    return {
                        user_id: userId,
                        name: userName,
                        score: score,
                        correct_answers: correctAnswers,
                        total_answers: totalAnswers,
                        accuracy: accuracy,
                        last_answer_time: data.last_answer_time || 0,
                        status: data.status || 'in_progress'
                    };
                })
            );

            // Sắp xếp bảng xếp hạng theo:
            // 1. Điểm số (cao nhất lên đầu)
            // 2. Số câu đúng (nhiều nhất lên đầu)
            // 3. Thời gian trả lời cuối (nhanh nhất lên đầu)
            const sortedLeaderboard = leaderboardData.sort((a, b) => {
                // So sánh điểm số
                if (b.score !== a.score) {
                    return b.score - a.score;
                }
                // Nếu điểm bằng nhau, so sánh số câu đúng
                if (b.correct_answers !== a.correct_answers) {
                    return b.correct_answers - a.correct_answers;
                }
                // Nếu số câu đúng bằng nhau, so sánh thời gian trả lời cuối
                return a.last_answer_time - b.last_answer_time;
            });

            // Thêm vị trí và thông tin thay đổi
            return sortedLeaderboard.map((entry, index) => ({
                ...entry,
                position: index + 1,
                previous_position: entry.previous_position || index + 1,
                is_ahead: entry.previous_position > index + 1,
                is_behind: entry.previous_position < index + 1
            }));
        } catch (error) {
            console.error('Lỗi trong getRealtimeLeaderboard:', error);
            return [];
        }
    }

    // Đồng bộ dữ liệu quiz từ Firebase về DB
    async syncQuizDataToDatabase(quizId) {
        const quizRef = db.ref(`quiz_sessions/${quizId}`);
        const snapshot = await quizRef.once('value');
        const quizData = snapshot.val();

        if (!quizData || !quizData.participants) return;

        // Log dữ liệu của user_id = 10
        if (quizData.participants['10']) {
            console.log('Found user_id = 10 in Firebase:');
            console.log('Quiz ID:', quizId);
            console.log('User Data:', JSON.stringify(quizData.participants['10'], null, 2));
            console.log('Answers:', JSON.stringify(quizData.participants['10'].answers, null, 2));
            console.log('Current Question:', quizData.participants['10'].current_question_id);
            console.log('Score:', quizData.participants['10'].current_score);
            console.log('Status:', quizData.participants['10'].status);
            console.log('Last Answer Time:', new Date(quizData.participants['10'].last_answer_time).toLocaleString());
        }

        for (const [userId, userData] of Object.entries(quizData.participants)) {
            // Kiểm tra user tồn tại trong DB
            const user = await User.findByPk(userId);
            if (!user) {
                console.warn(`User ${userId} not found in DB, skip history insert`);
                // Log thêm thông tin về user không tồn tại
                if (userId === '10') {
                    console.log('User 10 not found in DB. Attempting to remove from Firebase...');
                    try {
                        await db.ref(`quiz_sessions/${quizId}/participants/10`).remove();
                        console.log('Successfully removed user 10 from Firebase');
                    } catch (error) {
                        console.error('Error removing user 10 from Firebase:', error);
                    }
                }
                continue; // Bỏ qua nếu user không tồn tại
            }

            // 1. Lưu từng câu hỏi vào UserQuestionHistory
            for (const [questionId, answerData] of Object.entries(userData.answers || {})) {
                try {
                    await UserQuestionHistory.create({
                        user_id: userId,
                        question_id: questionId,
                        quiz_id: quizId,
                        selected_answer: answerData.answer_id,
                        is_correct: answerData.is_correct,
                        time_spent: answerData.response_time,
                        attempt_date: new Date(answerData.timestamp)
                    });
                } catch (err) {
                    console.error(`Error saving UserQuestionHistory for user ${userId}, question ${questionId}:`, err.message);
                }
            }

            // 2. Tính toán mastery LO và lưu vào UserLOTracking
            // Gom nhóm theo lo_id
            const loStats = {};
            for (const [questionId, answerData] of Object.entries(userData.answers || {})) {
                const question = await Question.findByPk(questionId);
                if (!question) continue;
                const loId = question.lo_id;
                if (!loStats[loId]) {
                    loStats[loId] = { total: 0, correct: 0 };
                }
                loStats[loId].total++;
                if (answerData.is_correct) loStats[loId].correct++;
            }
            for (const [loId, stat] of Object.entries(loStats)) {
                // Lấy tất cả chapter_id từ ChapterLO với lo_id
                const chapterLOs = await ChapterLO.findAll({ where: { lo_id: loId } });
                const chapterIds = chapterLOs.map(clo => clo.chapter_id);
                if (!chapterIds.length) continue;
                // Lấy tất cả subject_id từ Chapter
                const chapters = await Chapter.findAll({ where: { chapter_id: chapterIds } });
                const subjectIds = [...new Set(chapters.map(chap => chap.subject_id))];
                if (!subjectIds.length) continue;
                // Lưu tracking cho từng subject_id
                for (const subjectId of subjectIds) {
                    const [tracking, created] = await UserLOTracking.findOrCreate({
                        where: { user_id: userId, lo_id: loId, subject_id: subjectId },
                        defaults: {
                            performance_metrics: {
                                total_attempts: stat.total,
                                correct_answers: stat.correct,
                                average_score: stat.total > 0 ? stat.correct / stat.total : 0,
                                last_attempt_date: new Date()
                            },
                            update_time: new Date()
                        }
                    });
                    if (!created) {
                        // Nếu đã có, cập nhật lại
                        const perf = tracking.performance_metrics || {};
                        const total_attempts = (perf.total_attempts || 0) + stat.total;
                        const correct_answers = (perf.correct_answers || 0) + stat.correct;
                        tracking.performance_metrics = {
                            total_attempts,
                            correct_answers,
                            average_score: total_attempts > 0 ? correct_answers / total_attempts : 0,
                            last_attempt_date: new Date()
                        };
                        tracking.update_time = new Date();
                        await tracking.save();
                    }
                }
            }

            // 3. Cập nhật UserQuizTracking tổng thể quiz
            const allAnswers = Object.values(userData.answers || {});
            const totalQuestions = allAnswers.length;
            const correctAnswers = allAnswers.filter(a => a.is_correct).length;
            const averageScore = totalQuestions > 0 ? correctAnswers / totalQuestions : 0;
            let completionTime = null;
            if (allAnswers.length > 0) {
                const timestamps = allAnswers.map(a => a.timestamp).sort();
                completionTime = (timestamps[timestamps.length - 1] - timestamps[0]) / 1000; // giây
            }
            await UserQuizTracking.upsert({
                user_id: userId,
                quiz_id: quizId,
                performance_metrics: {
                    total_attempts: 1, // mỗi lần sync là 1 attempt mới
                    average_score: averageScore,
                    best_score: averageScore, // có thể cập nhật lại nếu muốn lưu nhiều lần làm
                    completion_time: completionTime,
                    last_attempt_date: new Date()
                },
                difficulty_breakdown: {}, // có thể bổ sung nếu muốn
                lo_performance: {}, // có thể bổ sung nếu muốn
                update_time: new Date()
            });

            // 4. Đồng bộ điểm cuối cùng vào QuizResult
            const score = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 10 : 0;
            let quizResultCompletionTime = null;
            if (allAnswers.length > 0) {
                const timestamps = allAnswers.map(a => a.timestamp).sort();
                if (timestamps.length > 1) {
                    quizResultCompletionTime = Math.round((timestamps[timestamps.length - 1] - timestamps[0]) / 1000); // số giây
                } else {
                    quizResultCompletionTime = null;
                }
            }
            // Log debug giá trị trước khi lưu
            console.log('QuizResult save:', {
                quiz_id: quizId,
                user_id: userId,
                score,
                status: 'completed',
                completion_time: quizResultCompletionTime,
                update_time: new Date()
            });
            // Dùng findOrCreate + update thay vì upsert
            const [result, created] = await QuizResult.findOrCreate({
                where: { quiz_id: quizId, user_id: userId },
                defaults: {
                    score,
                    status: 'completed',
                    completion_time: quizResultCompletionTime,
                    update_time: new Date()
                }
            });
            if (!created) {
                result.score = score;
                result.status = 'completed';
                result.completion_time = quizResultCompletionTime;
                result.update_time = new Date();
                await result.save();
            }
        }

        // Xóa dữ liệu quiz khỏi Firebase sau khi đã đồng bộ
        await quizRef.remove();
    }
}

module.exports = QuizRealtimeService;
