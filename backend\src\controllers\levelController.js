const { Level, Question } = require('../models');

exports.getAllLevels = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const levels = await Level.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [{ model: Question, attributes: ['question_id', 'question_text'] }],
        });

        res.status(200).json({
            totalItems: levels.count,
            totalPages: Math.ceil(levels.count / limit),
            currentPage: parseInt(page),
            levels: levels.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách Level', error: error.message });
    }
};

exports.getLevelById = async (req, res) => {
    try {
        const level = await Level.findByPk(req.params.id, {
            include: [{ model: Question, attributes: ['question_id', 'question_text'] }],
        });

        if (!level) return res.status(404).json({ message: 'Level không tồn tại' });
        res.status(200).json(level);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin Level', error: error.message });
    }
};

exports.createLevel = async (req, res) => {
    try {
        const { name } = req.body;

        if (!name) {
            return res.status(400).json({ message: 'Tên Level là bắt buộc' });
        }

        const newLevel = await Level.create({ name });
        res.status(201).json(newLevel);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo Level', error: error.message });
    }
};

exports.updateLevel = async (req, res) => {
    try {
        const { name } = req.body;

        const level = await Level.findByPk(req.params.id);
        if (!level) return res.status(404).json({ message: 'Level không tồn tại' });

        await level.update({ name: name || level.name });
        res.status(200).json(level);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật Level', error: error.message });
    }
};

exports.deleteLevel = async (req, res) => {
    try {
        const level = await Level.findByPk(req.params.id);
        if (!level) return res.status(404).json({ message: 'Level không tồn tại' });

        await level.destroy();
        res.status(200).json({ message: 'Xóa Level thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa Level', error: error.message });
    }
};