/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React from "react";
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
} from "chart.js";
import { Radar } from "react-chartjs-2";
import { RadarChartData, RadarChartConfig } from "@/types/radar";

// Đăng ký các component cần thiết cho Chart.js
ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
);

interface RadarChartProps {
  data: RadarChartConfig;
  title?: string;
  height?: number;
  className?: string;
}

export default function RadarChart({
  data,
  title,
  height = 400,
  className = "",
}: RadarChartProps) {
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          font: {
            size: 12,
          },
          padding: 20,
        },
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "white",
        bodyColor: "white",
        borderColor: "rgba(255, 255, 255, 0.2)",
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function (context: any) {
            const label = context.dataset.label || "";
            const value = context.parsed.r;
            return `${label}: ${value}%`;
          },
          afterLabel: function (context: any) {
            const labelIndex = context.dataIndex;
            const chartData = context.chart.data;
            const labelName = chartData.labels[labelIndex];

            // Thêm thông tin mô tả nếu có
            if (typeof labelName === "string" && labelName.includes("LO")) {
              return "Chuẩn đầu ra học tập";
            }
            return "";
          },
        },
      },
    },
    scales: {
      r: {
        beginAtZero: true,
        max: 100,
        min: 0,
        ticks: {
          stepSize: 20,
          font: {
            size: 10,
          },
          color: "rgba(107, 114, 128, 0.8)",
          callback: function (value: any) {
            return value + "%";
          },
        },
        grid: {
          color: "rgba(107, 114, 128, 0.2)",
        },
        angleLines: {
          color: "rgba(107, 114, 128, 0.2)",
        },
        pointLabels: {
          font: {
            size: 11,
            weight: 500,
          },
          color: "rgba(55, 65, 81, 0.9)",
          padding: 10,
        },
      },
    },
    elements: {
      point: {
        radius: 4,
        hoverRadius: 6,
      },
      line: {
        borderWidth: 2,
      },
    },
  } as const;

  return (
    <div className={`w-full ${className}`}>
      {title && (
        <h3 className="text-lg font-semibold text-center mb-4 text-gray-800">
          {title}
        </h3>
      )}
      <div style={{ height: `${height}px` }}>
        <Radar data={data} options={options} />
      </div>
    </div>
  );
}

// Utility function để chuyển đổi radar data thành format Chart.js
export function transformRadarData(
  radarData: RadarChartData,
  label: string,
  colors: {
    backgroundColor: string;
    borderColor: string;
    pointBackgroundColor: string;
  }
): RadarChartConfig {
  // Kết hợp difficulty levels và learning outcomes
  const difficultyLabels = Object.keys(radarData.difficulty_levels).map(
    (level) => `Mức độ ${level.charAt(0).toUpperCase() + level.slice(1)}`
  );
  const difficultyData = Object.values(radarData.difficulty_levels).map(
    (data) => data.accuracy
  );

  const loLabels = Object.keys(radarData.learning_outcomes).map((lo) => lo);
  const loData = Object.values(radarData.learning_outcomes).map(
    (data) => data.accuracy
  );

  // Thêm performance metrics
  const performanceLabels = [
    "Độ chính xác tổng thể",
    "Độ chính xác lần đầu",
    "Tỷ lệ hoàn thành",
  ];
  const performanceData = [
    radarData.performance_metrics.overall_accuracy,
    radarData.performance_metrics.first_attempt_accuracy,
    radarData.performance_metrics.completion_rate,
  ];

  return {
    labels: [...difficultyLabels, ...loLabels, ...performanceLabels],
    datasets: [
      {
        label,
        data: [...difficultyData, ...loData, ...performanceData],
        backgroundColor: colors.backgroundColor,
        borderColor: colors.borderColor,
        borderWidth: 2,
        pointBackgroundColor: colors.pointBackgroundColor,
        pointBorderColor: colors.borderColor,
        pointHoverBackgroundColor: colors.borderColor,
        pointHoverBorderColor: "#fff",
        fill: true,
      },
    ],
  };
}

// Predefined color schemes
export const colorSchemes = {
  primary: {
    backgroundColor: "rgba(59, 130, 246, 0.2)",
    borderColor: "rgb(59, 130, 246)",
    pointBackgroundColor: "rgb(59, 130, 246)",
  },
  success: {
    backgroundColor: "rgba(34, 197, 94, 0.2)",
    borderColor: "rgb(34, 197, 94)",
    pointBackgroundColor: "rgb(34, 197, 94)",
  },
  warning: {
    backgroundColor: "rgba(251, 191, 36, 0.2)",
    borderColor: "rgb(251, 191, 36)",
    pointBackgroundColor: "rgb(251, 191, 36)",
  },
  danger: {
    backgroundColor: "rgba(239, 68, 68, 0.2)",
    borderColor: "rgb(239, 68, 68)",
    pointBackgroundColor: "rgb(239, 68, 68)",
  },
  purple: {
    backgroundColor: "rgba(147, 51, 234, 0.2)",
    borderColor: "rgb(147, 51, 234)",
    pointBackgroundColor: "rgb(147, 51, 234)",
  },
};
