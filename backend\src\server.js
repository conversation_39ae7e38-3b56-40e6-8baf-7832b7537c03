const server = require('./app');
const { checkAndEndExpiredQuizzes } = require('./controllers/quizController');

const PORT = process.env.PORT || 8888;

server.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
    console.log(`WebSocket server is running on ws://localhost:${PORT}`);

    // Bắt đầu kiểm tra quiz hết thời gian
    setInterval(() => {
        checkAndEndExpiredQuizzes(server.io);
    }, 60000);
}); 