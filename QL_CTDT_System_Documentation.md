# Tài liệu Hệ thống QL_CTDT (Quản lý Chương trình Đào tạo)

## 📋 Tổng quan Hệ thống

**QL_CTDT** là một hệ thống quản lý chương trình đào tạo hiện đại, đ<PERSON><PERSON><PERSON> xây dựng với kiến trúc full-stack, hỗ trợ thi trực tuyến realtime và quản lý học tập toàn diện.

### 🎯 Mục đích chính
- Quản lý chương trình đào tạo (Programs, PO, PLO)
- Quản lý môn học và khóa học (Courses, Subjects, Chapters)
- <PERSON>ệ thống thi trực tuyến realtime với Socket.IO
- Quản lý người dùng và phân quyền (Admin, Teacher, Student)
- <PERSON> k<PERSON> qu<PERSON> học tập và analytics
- Quản lý câu hỏi và bài kiểm tra

## 🏗️ Kiến trúc Hệ thống

### Sơ đồ Kiến trúc Tổng quan

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Browser]
        MOBILE[Mobile Browser]
    end

    subgraph "Load Balancer & Reverse Proxy"
        NGINX[Nginx<br/>- SSL Termination<br/>- Load Balancing<br/>- Static Files]
    end

    subgraph "Frontend Layer"
        NEXTJS[Next.js Frontend<br/>- React 19<br/>- TypeScript<br/>- Tailwind CSS<br/>- Socket.IO Client]
    end

    subgraph "Backend Layer"
        API[Express.js API Server<br/>- RESTful APIs<br/>- JWT Authentication<br/>- Role-based Authorization]
        SOCKET[Socket.IO Server<br/>- Real-time Quiz<br/>- Live Updates<br/>- WebSocket Management]
        MIDDLEWARE[Middleware Layer<br/>- Auth Middleware<br/>- Quiz Session<br/>- File Upload]
    end

    subgraph "Business Logic Layer"
        CONTROLLERS[Controllers<br/>- User Controller<br/>- Quiz Controller<br/>- Course Controller<br/>- Program Controller]
        SERVICES[Services<br/>- Quiz Realtime Service<br/>- Auth Service<br/>- File Service]
    end

    subgraph "Data Access Layer"
        ORM[Sequelize ORM<br/>- Model Definitions<br/>- Relationships<br/>- Migrations]
    end

    subgraph "Database Layer"
        POSTGRES[(PostgreSQL<br/>- User Data<br/>- Course Data<br/>- Quiz Data<br/>- Results)]
        REDIS[(Redis Cache<br/>- Session Storage<br/>- Quiz State<br/>- Real-time Data)]
    end

    subgraph "External Services"
        FIREBASE[Firebase<br/>- Real-time Database<br/>- Push Notifications]
        STORAGE[File Storage<br/>- Question Images<br/>- Course Materials]
    end

    subgraph "Infrastructure"
        DOCKER[Docker Containers<br/>- Frontend Container<br/>- Backend Container<br/>- Database Containers]
    end

    %% Connections
    WEB --> NGINX
    MOBILE --> NGINX
    NGINX --> NEXTJS
    NGINX --> API
    NGINX --> SOCKET

    NEXTJS --> API
    NEXTJS --> SOCKET

    API --> MIDDLEWARE
    SOCKET --> MIDDLEWARE
    MIDDLEWARE --> CONTROLLERS
    CONTROLLERS --> SERVICES
    SERVICES --> ORM

    ORM --> POSTGRES
    SERVICES --> REDIS
    SERVICES --> FIREBASE
    CONTROLLERS --> STORAGE

    DOCKER -.-> NEXTJS
    DOCKER -.-> API
    DOCKER -.-> SOCKET
    DOCKER -.-> POSTGRES
    DOCKER -.-> REDIS

    %% Styling
    classDef frontend fill:#e1f5fe
    classDef backend fill:#f3e5f5
    classDef database fill:#e8f5e8
    classDef external fill:#fff3e0
    classDef infrastructure fill:#fce4ec

    class NEXTJS frontend
    class API,SOCKET,MIDDLEWARE,CONTROLLERS,SERVICES,ORM backend
    class POSTGRES,REDIS database
    class FIREBASE,STORAGE external
    class DOCKER infrastructure
```

### Frontend Layer
- **Framework**: Next.js 15 với React 19
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI, Lucide React
- **State Management**: React Hooks, Custom Hooks
- **Real-time**: Socket.IO Client
- **Authentication**: JWT với role-based access control

### Backend Layer
- **Runtime**: Node.js
- **Framework**: Express.js
- **Real-time**: Socket.IO Server
- **ORM**: Sequelize
- **Authentication**: JWT, bcrypt
- **File Upload**: Multer
- **Validation**: Custom middleware

### Database Layer
- **Primary Database**: PostgreSQL
  - User data, Course data, Quiz data, Results
- **Cache Database**: Redis
  - Session storage, Quiz state, Real-time data
- **External Database**: Firebase
  - Real-time database, Push notifications

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Reverse Proxy**: Nginx
- **SSL**: Let's Encrypt certificates
- **File Storage**: Local storage với upload handling

## 👥 Vai trò Người dùng

### Sơ đồ Use Case UML - Phần 1: Quản lý Hệ thống & Giảng dạy

```mermaid
graph TB
    %% Actors
    Admin((Admin))
    Teacher((Giảng viên))

    %% System boundary
    subgraph "Hệ thống QL_CTDT - Quản lý & Giảng dạy"
        %% Admin Use Cases
        UC1((Quản lý<br/>người dùng))
        UC2((Phân quyền<br/>vai trò))
        UC3((Quản lý chương<br/>trình đào tạo))
        UC4((Cấu hình<br/>hệ thống))
        UC5((Xem báo cáo<br/>tổng quan))

        %% Teacher Use Cases
        UC6((Quản lý<br/>môn học))
        UC7((Tạo/Chỉnh sửa<br/>câu hỏi))
        UC8((Tạo bài<br/>kiểm tra))
        UC9((Quản lý quiz<br/>realtime))
        UC10((Xem kết quả<br/>học sinh))
        UC11((Xuất<br/>báo cáo))
        UC12((Upload<br/>tài liệu))

        %% Common Use Cases
        UC18((Đăng nhập))
        UC19((Đăng xuất))
        UC20((Quản lý<br/>profile))
    end

    %% Admin relationships
    Admin --- UC1
    Admin --- UC2
    Admin --- UC3
    Admin --- UC4
    Admin --- UC5
    Admin --- UC18
    Admin --- UC19
    Admin --- UC20

    %% Teacher relationships
    Teacher --- UC6
    Teacher --- UC7
    Teacher --- UC8
    Teacher --- UC9
    Teacher --- UC10
    Teacher --- UC11
    Teacher --- UC12
    Teacher --- UC18
    Teacher --- UC19
    Teacher --- UC20

    %% Include relationships
    UC8 -.->|include| UC7
    UC11 -.->|extend| UC10
    UC5 -.->|extend| UC1

    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef usecase fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef system fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px

    class Admin,Teacher actor
    class UC1,UC2,UC3,UC4,UC5,UC6,UC7,UC8,UC9,UC10,UC11,UC12,UC18,UC19,UC20 usecase
```

### Sơ đồ Use Case UML - Phần 2: Học tập & Quiz Realtime

```mermaid
graph TB
    %% Actors
    Teacher((Giảng viên))
    Student((Sinh viên))

    %% System boundary
    subgraph "Hệ thống QL_CTDT - Học tập & Quiz"
        %% Student Use Cases
        UC13((Đăng ký<br/>môn học))
        UC14((Tham gia quiz<br/>realtime))
        UC15((Xem kết quả<br/>bài thi))
        UC16((Theo dõi tiến<br/>độ học tập))
        UC17((Tải tài liệu<br/>môn học))

        %% Common Use Cases
        UC18((Đăng nhập))
        UC19((Đăng xuất))
        UC20((Quản lý<br/>profile))

        %% Quiz Realtime Use Cases
        UC21((Tạo phòng<br/>quiz))
        UC22((Tham gia<br/>phòng chờ))
        UC23((Bắt đầu<br/>quiz))
        UC24((Trả lời<br/>câu hỏi))
        UC25((Xem bảng<br/>xếp hạng))
        UC26((Kết thúc<br/>quiz))
    end

    %% Teacher relationships (Quiz related)
    Teacher --- UC21
    Teacher --- UC23
    Teacher --- UC25
    Teacher --- UC26
    Teacher --- UC18
    Teacher --- UC19
    Teacher --- UC20

    %% Student relationships
    Student --- UC13
    Student --- UC14
    Student --- UC15
    Student --- UC16
    Student --- UC17
    Student --- UC18
    Student --- UC19
    Student --- UC20
    Student --- UC22
    Student --- UC24
    Student --- UC25

    %% Include relationships
    UC14 -.->|include| UC22
    UC14 -.->|include| UC24

    %% Extend relationships
    UC25 -.->|extend| UC14
    UC25 -.->|extend| UC23

    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef usecase fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef system fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px

    class Teacher,Student actor
    class UC13,UC14,UC15,UC16,UC17,UC18,UC19,UC20,UC21,UC22,UC23,UC24,UC25,UC26 usecase
```

### 🔧 Admin
- Quản lý người dùng (CRUD operations)
- Phân quyền vai trò
- Quản lý chương trình đào tạo
- Cấu hình hệ thống
- Xem báo cáo tổng quan

### 👨‍🏫 Giảng viên (Teacher)
- Quản lý môn học
- Tạo/Chỉnh sửa câu hỏi
- Tạo bài kiểm tra
- Quản lý quiz realtime
- Xem kết quả học sinh
- Xuất báo cáo
- Upload tài liệu

### 👨‍🎓 Sinh viên (Student)
- Đăng ký môn học
- Tham gia quiz realtime
- Xem kết quả bài thi
- Theo dõi tiến độ học tập
- Tải tài liệu môn học

## 🎮 Tính năng Quiz Realtime

### Đặc điểm nổi bật
- **Real-time synchronization**: Đồng bộ thời gian thực với Socket.IO
- **Live leaderboard**: Bảng xếp hạng cập nhật trực tiếp
- **Multi-room support**: Hỗ trợ nhiều phòng quiz đồng thời
- **Auto-progression**: Tự động chuyển câu hỏi
- **Session management**: Quản lý phiên làm bài với Redis
- **Analytics tracking**: Theo dõi chi tiết quá trình làm bài

### Sơ đồ Luồng Quiz Realtime

```mermaid
sequenceDiagram
    participant T as 👨‍🏫 Giảng viên
    participant S as 👨‍🎓 Sinh viên
    participant FE as Frontend
    participant BE as Backend API
    participant WS as Socket.IO Server
    participant R as Redis Cache
    participant FB as Firebase
    participant DB as PostgreSQL

    Note over T,DB: Giai đoạn 1: Tạo và Chuẩn bị Quiz

    T->>FE: Tạo quiz mới
    FE->>BE: POST /api/quizzes
    BE->>DB: Lưu thông tin quiz
    DB-->>BE: Quiz ID
    BE-->>FE: Quiz được tạo
    FE-->>T: Hiển thị quiz dashboard

    T->>FE: Bắt đầu quiz
    FE->>BE: POST /api/quizzes/{id}/start
    BE->>R: Tạo quiz session
    BE->>FB: Tạo quiz room
    BE->>WS: Khởi tạo Socket rooms
    WS-->>BE: Rooms created
    BE-->>FE: Quiz started

    Note over T,DB: Giai đoạn 2: Sinh viên Tham gia

    S->>FE: Nhập mã PIN quiz
    FE->>BE: POST /api/quizzes/join
    BE->>R: Kiểm tra quiz session
    R-->>BE: Session valid
    BE->>FB: Thêm participant
    BE->>WS: Join quiz rooms
    WS->>WS: Emit 'newParticipant'
    WS-->>FE: Participant joined
    FE-->>S: Vào phòng chờ

    Note over T,DB: Giai đoạn 3: Bắt đầu Quiz Realtime

    T->>FE: Start quiz
    FE->>BE: POST /api/quizzes/{id}/start-auto
    BE->>R: Lấy danh sách câu hỏi
    BE->>WS: Emit 'quizStarted'
    WS-->>FE: Quiz bắt đầu
    FE-->>S: Chuyển đến trang quiz
    FE-->>T: Chuyển đến trang quiz

    loop Mỗi câu hỏi
        BE->>WS: Emit 'newQuestion'
        WS-->>FE: Nhận câu hỏi mới
        FE-->>S: Hiển thị câu hỏi
        FE-->>T: Hiển thị câu hỏi

        Note over S,R: Sinh viên trả lời
        S->>FE: Chọn đáp án
        FE->>BE: POST /api/quizzes/realtime/answer
        BE->>R: Lưu câu trả lời
        BE->>DB: Lưu kết quả
        BE->>FB: Cập nhật điểm số

        Note over BE,WS: Tính toán và cập nhật
        BE->>BE: Tính điểm
        BE->>R: Cập nhật leaderboard
        BE->>WS: Emit 'showAnswerResult'
        WS-->>FE: Hiển thị kết quả
        FE-->>S: Đáp án đúng/sai

        Note over BE,WS: Chuyển câu tiếp theo
        BE->>WS: Emit 'nextQuestion' (sau 3s)
        WS-->>FE: Câu hỏi tiếp theo
    end

    Note over T,DB: Giai đoạn 4: Kết thúc và Bảng xếp hạng

    BE->>R: Tính toán bảng xếp hạng cuối
    BE->>WS: Emit 'quizCompleted'
    WS-->>FE: Quiz hoàn thành
    FE-->>S: Hiển thị kết quả cá nhân
    FE-->>T: Hiển thị bảng xếp hạng

    T->>FE: Xem báo cáo chi tiết
    FE->>BE: GET /api/quiz-results/{id}
    BE->>DB: Truy vấn kết quả
    DB-->>BE: Dữ liệu kết quả
    BE-->>FE: Báo cáo chi tiết
    FE-->>T: Dashboard kết quả

    Note over T,DB: Giai đoạn 5: Cleanup

    BE->>R: Xóa quiz session
    BE->>FB: Cleanup quiz room
    BE->>WS: Disconnect rooms
    WS-->>BE: Rooms cleaned

    Note over T,DB: Các tính năng bổ sung

    rect rgb(255, 248, 220)
        Note over FE,R: Realtime Features
        WS->>FE: Live participant count
        WS->>FE: Live leaderboard updates
        WS->>FE: Question timer sync
        WS->>FE: Connection status
    end

    rect rgb(240, 248, 255)
        Note over BE,DB: Analytics & Tracking
        BE->>DB: User question history
        BE->>DB: Quiz analytics
        BE->>DB: Learning path tracking
        BE->>R: Performance metrics
    end
```

### Luồng hoạt động
1. **Tạo Quiz**: Giảng viên tạo quiz và câu hỏi
2. **Khởi tạo Session**: Tạo phòng chờ với mã PIN
3. **Tham gia**: Sinh viên nhập mã PIN để tham gia
4. **Bắt đầu**: Giảng viên khởi động quiz
5. **Làm bài**: Sinh viên trả lời câu hỏi realtime
6. **Kết quả**: Hiển thị đáp án và điểm số ngay lập tức
7. **Bảng xếp hạng**: Cập nhật thứ hạng theo thời gian thực
8. **Báo cáo**: Xuất kết quả chi tiết

## 🗄️ Cấu trúc Cơ sở Dữ liệu

### Sơ đồ Entity Relationship Diagram (ERD)

```mermaid
erDiagram
    %% Core Entities
    Programs {
        int program_id PK
        string name
        text description
    }

    Roles {
        int role_id PK
        string name
    }

    Users {
        int user_id PK
        string name
        string email UK
        string password
        int role_id FK
    }

    %% Program Structure
    POs {
        int po_id PK
        string name
        text description
        int program_id FK
    }

    PLOs {
        int plo_id PK
        text description
        int program_id FK
    }

    POsPLOs {
        int po_id FK
        int plo_id FK
    }

    %% Course Structure
    Courses {
        int course_id PK
        string name
        text description
        int program_id FK
        int user_id FK
    }

    StudentCourses {
        int user_id FK
        int course_id FK
    }

    TypeSubjects {
        int type_id PK
        text description
    }

    TypeOfKnowledges {
        int noidung_id PK
        text description
    }

    Groups {
        int group_id PK
        text description
    }

    Subjects {
        int subject_id PK
        string name
        text description
        int course_id FK
        int type_id FK
        int noidung_id FK
        int plo_id FK
    }

    Chapters {
        int chapter_id PK
        string name
        text description
        int subject_id FK
    }

    LOs {
        int lo_id PK
        string name
        text description
    }

    ChapterLOs {
        int chapter_id FK
        int lo_id FK
    }

    %% Quiz Structure
    QuestionTypes {
        int type_id PK
        string name
    }

    Levels {
        int level_id PK
        string name
    }

    Questions {
        int question_id PK
        text content
        text explanation
        int lo_id FK
        int type_id FK
        int level_id FK
        int group_id FK
    }

    Answers {
        int answer_id PK
        text content
        boolean is_correct
        int question_id FK
    }

    Quizzes {
        int quiz_id PK
        string name
        text description
        int subject_id FK
        int time_limit
        boolean is_active
    }

    QuizQuestions {
        int quiz_id FK
        int question_id FK
        int order_index
    }

    %% Results
    QuizResults {
        int result_id PK
        int user_id FK
        int quiz_id FK
        int score
        datetime completed_at
    }

    CourseResults {
        int result_id PK
        int user_id FK
        int course_id FK
        float final_score
        string grade
    }

    %% Prerequisites
    TienQuyets {
        int subject_id FK
        int prerequisite_subject_id FK
    }

    %% Analytics & Tracking
    QuizAnalytics {
        int analytics_id PK
        int quiz_id FK
        int total_participants
        float average_score
        datetime created_at
    }

    UserQuizTracking {
        int tracking_id PK
        int user_id FK
        int quiz_id FK
        int current_question_index
        datetime started_at
        string status
    }

    UserQuestionHistory {
        int history_id PK
        int user_id FK
        int question_id FK
        int selected_answer_id FK
        boolean is_correct
        datetime answered_at
    }

    UserLearningPath {
        int path_id PK
        int user_id FK
        int lo_id FK
        string mastery_level
        datetime last_practiced
    }

    %% Relationships
    Programs ||--o{ POs : has
    Programs ||--o{ PLOs : has
    Programs ||--o{ Courses : contains

    POs ||--o{ POsPLOs : links
    PLOs ||--o{ POsPLOs : links

    Roles ||--o{ Users : assigns

    Users ||--o{ Courses : teaches
    Users ||--o{ StudentCourses : enrolls
    Users ||--o{ QuizResults : takes
    Users ||--o{ CourseResults : achieves
    Users ||--o{ UserQuizTracking : tracks
    Users ||--o{ UserQuestionHistory : records
    Users ||--o{ UserLearningPath : follows

    Courses ||--o{ StudentCourses : includes
    Courses ||--o{ Subjects : contains
    Courses ||--o{ CourseResults : evaluates

    TypeSubjects ||--o{ Subjects : categorizes
    TypeOfKnowledges ||--o{ Subjects : classifies
    PLOs ||--o{ Subjects : maps

    Subjects ||--o{ Chapters : organizes
    Subjects ||--o{ Quizzes : tests
    Subjects ||--o{ TienQuyets : requires
    Subjects ||--o{ TienQuyets : prerequisite

    Chapters ||--o{ ChapterLOs : contains
    LOs ||--o{ ChapterLOs : belongs
    LOs ||--o{ Questions : covers
    LOs ||--o{ UserLearningPath : guides

    QuestionTypes ||--o{ Questions : types
    Levels ||--o{ Questions : difficulty
    Groups ||--o{ Questions : groups

    Questions ||--o{ Answers : has
    Questions ||--o{ QuizQuestions : includes
    Questions ||--o{ UserQuestionHistory : answered

    Quizzes ||--o{ QuizQuestions : contains
    Quizzes ||--o{ QuizResults : generates
    Quizzes ||--o{ QuizAnalytics : analyzes
    Quizzes ||--o{ UserQuizTracking : monitors

    Answers ||--o{ UserQuestionHistory : selected
```

### Core Entities
- **Programs**: Chương trình đào tạo
- **PO/PLO**: Program Outcomes / Program Learning Outcomes
- **Courses**: Khóa học
- **Subjects**: Môn học
- **Chapters**: Chương học
- **LO**: Learning Outcomes

### Quiz System
- **Questions**: Câu hỏi với phân loại theo LO, độ khó
- **Answers**: Đáp án cho từng câu hỏi
- **Quizzes**: Bài kiểm tra
- **QuizResults**: Kết quả làm bài

### User Management
- **Users**: Thông tin người dùng
- **Roles**: Vai trò (Admin, Teacher, Student)
- **StudentCourses**: Đăng ký môn học

### Analytics & Tracking
- **UserQuizTracking**: Theo dõi quá trình làm bài
- **UserQuestionHistory**: Lịch sử trả lời câu hỏi
- **QuizAnalytics**: Phân tích kết quả quiz
- **UserLearningPath**: Lộ trình học tập cá nhân

## 🚀 Deployment

### Docker Compose Services
```yaml
services:
  - postgres: PostgreSQL database
  - redis: Redis cache
  - backend: Node.js API server
  - frontend: Next.js application
  - nginx: Reverse proxy & SSL termination
```

### Network Configuration
- **Frontend**: Port 3000
- **Backend**: Port 8888
- **PostgreSQL**: Port 5433
- **Redis**: Port 6379
- **Nginx**: Port 80/443 (HTTP/HTTPS)

### SSL & Security
- Let's Encrypt SSL certificates
- HTTPS redirect
- HSTS headers
- JWT authentication
- Password hashing with bcrypt

## 📊 API Endpoints

### Authentication
- `POST /api/users/login` - Đăng nhập
- `POST /api/users/register` - Đăng ký

### Quiz Management
- `GET /api/quizzes` - Danh sách quiz
- `POST /api/quizzes` - Tạo quiz mới
- `POST /api/quizzes/:id/start` - Bắt đầu quiz
- `POST /api/quizzes/:id/join` - Tham gia quiz
- `POST /api/quizzes/realtime/answer` - Gửi đáp án

### Course Management
- `GET /api/courses` - Danh sách khóa học
- `GET /api/subjects` - Danh sách môn học
- `GET /api/questions` - Danh sách câu hỏi

## 🔧 Công nghệ sử dụng

### Frontend Dependencies
```json
{
  "next": "15.3.0",
  "react": "^19.0.0",
  "typescript": "^5",
  "tailwindcss": "^4",
  "socket.io-client": "^4.8.1",
  "@radix-ui/react-*": "Latest",
  "axios": "^1.8.4"
}
```

### Backend Dependencies
```json
{
  "express": "^5.1.0",
  "socket.io": "^4.8.1",
  "sequelize": "^6.37.7",
  "pg": "^8.14.1",
  "redis": "^4.7.0",
  "jsonwebtoken": "^9.0.2",
  "bcrypt": "^5.1.1",
  "firebase-admin": "^13.2.0"
}
```

## 📈 Tính năng nâng cao

### Real-time Features
- Live participant count
- Live leaderboard updates
- Question timer synchronization
- Connection status monitoring
- Auto-reconnection handling

### Analytics & Reporting
- User performance tracking
- Question difficulty analysis
- Learning outcome assessment
- Progress visualization
- Export functionality (Excel, CSV)

### Security Features
- Role-based access control
- JWT token authentication
- Password encryption
- Session management
- CORS protection
- Rate limiting

## 🎨 UI/UX Features

### Modern Interface
- Dark/Light theme support
- Responsive design
- Loading states
- Error handling
- Toast notifications
- Modal dialogs

### Accessibility
- Keyboard navigation
- Screen reader support
- High contrast mode
- Focus management

## 📝 Kết luận

Hệ thống QL_CTDT là một giải pháp toàn diện cho việc quản lý chương trình đào tạo và tổ chức thi trực tuyến. Với kiến trúc hiện đại, tính năng realtime mạnh mẽ và giao diện thân thiện, hệ thống phù hợp cho các trường đại học, cao đẳng muốn số hóa quy trình giáo dục.

### Ưu điểm chính:
- ✅ Kiến trúc microservices dễ mở rộng
- ✅ Tính năng realtime mượt mà
- ✅ Bảo mật cao với JWT và role-based access
- ✅ UI/UX hiện đại và responsive
- ✅ Analytics và reporting chi tiết
- ✅ Deployment đơn giản với Docker

---
*Tài liệu được tạo tự động từ phân tích mã nguồn hệ thống QL_CTDT*
