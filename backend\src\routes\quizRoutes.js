// routes/quizRoutes.js
const express = require("express");
const router = express.Router();
const quizController = require("../controllers/quizController");
const { authenticateToken, authorize } = require("../middleware/authMiddleware");
const { checkQuizSession } = require('../middleware/quizSessionMiddleware');

// Public routes - không cần xác thực
router.get("/", quizController.getQuizzes);
router.get("/:id", quizController.getQuizById);
router.get("/:id/questions", quizController.getQuizQuestions);
router.get("/:id/leaderboard", quizController.getLeaderboard);
router.get("/pin/:pin", quizController.getQuizIdByPin);
// L<PERSON><PERSON> danh sách người tham gia quiz
router.get("/:id/participants", quizController.getQuizParticipants);
// Test route để trigger leaderboard (chỉ dùng cho development)
router.post("/:id/test-leaderboard", quizController.showLeaderboard);

// Teacher routes - chỉ giảng viên mới có quyền
router.post("/", authenticateToken, authorize(["teacher", "admin"]), quizController.createQuiz);
router.put("/:id", authenticateToken, authorize(["teacher", "admin"]), quizController.updateQuiz);
router.delete(
    "/:id",
    authenticateToken,
    authorize(["teacher", "admin"]),
    quizController.deleteQuiz
);
router.post(
    "/:id/start",
    authenticateToken,
    authorize(["teacher", "admin"]),
    quizController.startQuiz
);
/*router.post(
    "/:id/next",
    authenticateToken,
    authorize(["teacher", "admin"]),
    quizController.triggerNextQuestion
);*/
router.post(
    "/:id/leaderboard",
    authenticateToken,
    authorize(["teacher", "admin"]),
    quizController.showLeaderboard
);
router.post(
    "/:id/shuffle",
    authenticateToken,
    authorize(["teacher", "admin"]),
    quizController.shuffleQuestions
);
//router.post('/:id/auto', authenticateToken, authorize(["teacher", "admin"]), quizController.startAutoQuiz);
router.get(
    "/:id/realtime-scores",
    authenticateToken,
    authorize(["teacher", "admin"]),
    quizController.getRealtimeScores
);
router.get(
    "/:id/statistics",
    authenticateToken,
    authorize(["teacher", "admin"]),
    quizController.getQuizStatistics
);
router.get(
    "/:quizId/student/:userId/score-history",
    authenticateToken,
    authorize(["teacher", "admin"]),
    quizController.getStudentScoreHistory
);
router.get(
    "/:quizId/student/:userId/realtime",
    authenticateToken,
    authorize(["teacher", "admin"]),
    quizController.getStudentRealtimeData
);

// Student routes - chỉ học viên mới có quyền
router.post("/:id/submit", authenticateToken, authorize(["student"]), quizController.submitQuiz);
router.post("/:id/join", authenticateToken, authorize(["student"]), quizController.joinQuiz);
router.get(
    "/:id/current-question",
    authenticateToken,
    authorize(["student"]),
    quizController.getCurrentQuestion
);
router.get(
    "/:id/my-result",
    authenticateToken,
    authorize(["student"]),
    quizController.getMyResult
);
router.post('/:id/leave', authenticateToken, authorize(["student"]), quizController.leaveQuiz);

router.post('/realtime/answer', authenticateToken, authorize(["student"]), quizController.handleRealtimeAnswer);

router.get('/teacher/:user_id/quizzes', authenticateToken, authorize(['teacher']), quizController.getQuizzesByTeacherId);

// Thêm routes mới cho báo cáo
router.get('/:id/analytics', authenticateToken, authorize(['admin', 'teacher']), quizController.getQuizAnalytics);
router.get('/:quizId/participants/:userId', authenticateToken, authorize(['admin', 'teacher']), quizController.getParticipantDetail);

module.exports = router;
