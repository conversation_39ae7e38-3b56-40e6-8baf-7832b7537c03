const { PLO, Program, PO, Subject, LO } = require('../models');

exports.getAllPLOs = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;

        const plos = await PLO.findAndCountAll({
            limit: parseInt(limit),
            offset: parseInt(offset),
            include: [
                { model: Program, attributes: ['program_id', 'name'] },
                { model: PO, through: { attributes: [] }, attributes: ['po_id', 'name'] },
                { model: Subject, attributes: ['subject_id', 'name'] },
                { model: LO, attributes: ['lo_id', 'name'] },
            ],
        });

        res.status(200).json({
            totalItems: plos.count,
            totalPages: Math.ceil(plos.count / limit),
            currentPage: parseInt(page),
            plos: plos.rows,
        });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy danh sách PLO', error: error.message });
    }
};

exports.getPLOById = async (req, res) => {
    try {
        const plo = await PLO.findByPk(req.params.id, {
            include: [
                { model: Program, attributes: ['program_id', 'name'] },
                { model: PO, through: { attributes: [] }, attributes: ['po_id', 'name'] },
                { model: Subject, attributes: ['subject_id', 'name'] },
                { model: LO, attributes: ['lo_id', 'name'] },
            ],
        });

        if (!plo) return res.status(404).json({ message: 'PLO không tồn tại' });
        res.status(200).json(plo);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi lấy thông tin PLO', error: error.message });
    }
};

exports.createPLO = async (req, res) => {
    try {
        const { description, program_id } = req.body;

        if (!program_id) {
            return res.status(400).json({ message: 'Thiếu trường bắt buộc: program_id' });
        }

        const program = await Program.findByPk(program_id);
        if (!program) {
            return res.status(400).json({ message: 'Chương trình không tồn tại' });
        }

        const newPLO = await PLO.create({ description, program_id });
        res.status(201).json(newPLO);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi tạo PLO', error: error.message });
    }
};

exports.updatePLO = async (req, res) => {
    try {
        const { description, program_id } = req.body;

        const plo = await PLO.findByPk(req.params.id);
        if (!plo) return res.status(404).json({ message: 'PLO không tồn tại' });

        if (program_id) {
            const program = await Program.findByPk(program_id);
            if (!program) return res.status(400).json({ message: 'Chương trình không tồn tại' });
        }

        await plo.update({
            description: description || plo.description,
            program_id: program_id || plo.program_id,
        });

        res.status(200).json(plo);
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi cập nhật PLO', error: error.message });
    }
};

exports.deletePLO = async (req, res) => {
    try {
        const plo = await PLO.findByPk(req.params.id);
        if (!plo) return res.status(404).json({ message: 'PLO không tồn tại' });

        await plo.destroy();
        res.status(200).json({ message: 'Xóa PLO thành công' });
    } catch (error) {
        res.status(500).json({ message: 'Lỗi khi xóa PLO', error: error.message });
    }
};